#!/usr/bin/env python3
"""
90.5度切线修正逻辑验证脚本
验证在90.5度时，原始逻辑会导致切线穿过菱形内部，需要使用修正逻辑
"""

import numpy as np
import matplotlib.pyplot as plt
from main import HexagonFilmSimulation

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def find_corrected_leftmost_contact_point_90_5(sim, rotated_vertices, rotated_upper, test_angle):
    """
    针对90.5度的修正接触点计算
    """
    print(f"\n=== 90.5度修正接触点计算详细分析 ===")
    
    # 1. 原始最左侧点计算
    original_contact, original_type = sim.find_leftmost_valid_contact_point(
        sim.A, rotated_vertices, rotated_upper, test_angle
    )
    
    print(f"原始最左侧点: {original_type}")
    print(f"原始接触点坐标: ({original_contact[0]:.2f}, {original_contact[1]:.2f})")
    
    # 2. 计算到原始接触点的切线角度
    to_original = original_contact - sim.A
    original_tangent_angle = np.arctan2(to_original[1], to_original[0])
    original_tangent_angle_deg = np.rad2deg(original_tangent_angle)
    
    print(f"到原始接触点的切线角度: {original_tangent_angle_deg:.2f}°")
    
    # 3. 确定下一个顶点（V4）
    next_vertex = rotated_vertices[3]  # V4 (索引3)
    next_vertex_name = "V4"
    
    # 4. 计算到下一个顶点的连线角度
    to_next = next_vertex - sim.A
    next_vertex_angle = np.arctan2(to_next[1], to_next[0])
    next_vertex_angle_deg = np.rad2deg(next_vertex_angle)
    
    print(f"到下一个顶点{next_vertex_name}的连线角度: {next_vertex_angle_deg:.2f}°")
    print(f"下一个顶点{next_vertex_name}坐标: ({next_vertex[0]:.2f}, {next_vertex[1]:.2f})")
    
    # 5. 检查约束
    angle_diff = next_vertex_angle_deg - original_tangent_angle_deg
    print(f"角度差 (下一个顶点角度 - 原始切线角度): {angle_diff:.2f}°")
    
    if angle_diff > 0:
        print(f"⚠️  确认：切线会穿过菱形内部！")
        print(f"   原始切线角度({original_tangent_angle_deg:.2f}°) < 连线角度({next_vertex_angle_deg:.2f}°)")
        print(f"✅ 应用修正：使用{next_vertex_name}作为接触点")
        
        corrected_contact = next_vertex
        corrected_type = f"corrected_{next_vertex_name}"
        
        # 计算修正后的切线角度
        to_corrected = corrected_contact - sim.A
        corrected_tangent_angle = np.arctan2(to_corrected[1], to_corrected[0])
        corrected_tangent_angle_deg = np.rad2deg(corrected_tangent_angle)
        print(f"修正后的切线角度: {corrected_tangent_angle_deg:.2f}°")
        
        return original_contact, original_type, corrected_contact, corrected_type, True
    else:
        print(f"✅ 切线角度合理，无需修正")
        return original_contact, original_type, original_contact, original_type, False


def create_90_5_degree_comparison_visualization(sim, rotated_vertices, rotated_upper, 
                                               original_contact, original_type,
                                               corrected_contact, corrected_type, was_corrected):
    """
    创建90.5度原始vs修正的对比可视化
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # 图1: 原始逻辑（有问题）
    ax1.set_aspect("equal")
    ax1.grid(True, alpha=0.3)
    ax1.set_title("90.5度 - 原始逻辑（切线穿过菱形内部）", fontsize=14, fontweight="bold", color='red')
    
    # 绘制六边形
    hex_x = [v[0] for v in rotated_vertices] + [rotated_vertices[0][0]]
    hex_y = [v[1] for v in rotated_vertices] + [rotated_vertices[0][1]]
    ax1.plot(hex_x, hex_y, "b-", linewidth=2, label="六边形")
    
    # 绘制顶点
    for i, vertex in enumerate(rotated_vertices):
        ax1.plot(vertex[0], vertex[1], "bo", markersize=8)
        ax1.annotate(f"V{i+1}", (vertex[0], vertex[1]), xytext=(5, 5), 
                    textcoords="offset points", fontsize=10)
    
    # 绘制上方点
    ax1.plot(rotated_upper[0], rotated_upper[1], "go", markersize=8, label="上方点")
    
    # 绘制过辊
    roller_A = plt.Circle(sim.A, sim.roller_radius, color="orange", alpha=0.7, label="过辊A")
    ax1.add_patch(roller_A)
    ax1.plot(sim.A[0], sim.A[1], "ko", markersize=8)
    ax1.annotate("A", sim.A, xytext=(5, 5), textcoords="offset points", fontsize=12)
    
    # 绘制原始接触点和切线（红色，表示有问题）
    ax1.plot(original_contact[0], original_contact[1], "ro", markersize=12, label=f"原始接触点({original_type})")
    ax1.plot([sim.A[0], original_contact[0]], [sim.A[1], original_contact[1]], 
             "r--", linewidth=3, alpha=0.8, label="原始切线（穿过内部）")
    
    # 绘制到V4的连线（绿色虚线）
    v4 = rotated_vertices[3]
    ax1.plot([sim.A[0], v4[0]], [sim.A[1], v4[1]], 
             "g:", linewidth=2, alpha=0.8, label="到V4连线")
    
    ax1.set_xlim(-40, 40)
    ax1.set_ylim(-15, 90)
    ax1.legend()
    
    # 图2: 修正逻辑（正确）
    ax2.set_aspect("equal")
    ax2.grid(True, alpha=0.3)
    ax2.set_title("90.5度 - 修正逻辑（切线不穿过菱形内部）", fontsize=14, fontweight="bold", color='green')
    
    # 绘制六边形
    ax2.plot(hex_x, hex_y, "b-", linewidth=2, label="六边形")
    
    # 绘制顶点
    for i, vertex in enumerate(rotated_vertices):
        ax2.plot(vertex[0], vertex[1], "bo", markersize=8)
        ax2.annotate(f"V{i+1}", (vertex[0], vertex[1]), xytext=(5, 5), 
                    textcoords="offset points", fontsize=10)
    
    # 绘制上方点
    ax2.plot(rotated_upper[0], rotated_upper[1], "go", markersize=8, label="上方点")
    
    # 绘制过辊
    roller_A = plt.Circle(sim.A, sim.roller_radius, color="orange", alpha=0.7, label="过辊A")
    ax2.add_patch(roller_A)
    ax2.plot(sim.A[0], sim.A[1], "ko", markersize=8)
    ax2.annotate("A", sim.A, xytext=(5, 5), textcoords="offset points", fontsize=12)
    
    # 绘制修正后的接触点和切线（绿色，表示正确）
    ax2.plot(corrected_contact[0], corrected_contact[1], "go", markersize=12, label=f"修正接触点({corrected_type})")
    ax2.plot([sim.A[0], corrected_contact[0]], [sim.A[1], corrected_contact[1]], 
             "g-", linewidth=3, alpha=0.8, label="修正切线（正确）")
    
    # 显示原始接触点位置（半透明红色）
    ax2.plot(original_contact[0], original_contact[1], "ro", markersize=8, alpha=0.5, label="原始接触点（参考）")
    
    ax2.set_xlim(-40, 40)
    ax2.set_ylim(-15, 90)
    ax2.legend()
    
    # 设置总标题
    fig.suptitle('90.5度切线修正逻辑验证 - 原始vs修正对比', 
                 fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    plt.show()


def test_90_5_degree_correction():
    """
    测试90.5度的切线修正逻辑
    """
    print("开始测试90.5度切线修正逻辑...")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0, film_thickness=0.1)
    
    # 测试角度：90.5度
    test_angle = 90.5
    theta_rad = np.deg2rad(test_angle)
    
    print(f"测试角度: {test_angle}°")
    
    # 计算旋转后的顶点
    layer_num, accum_thickness = sim.calculate_layer_from_angle(test_angle)
    if accum_thickness > 0:
        current_vertices = sim.update_vertices_with_thickness(accum_thickness)
        rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
            theta_rad, current_vertices
        )
    else:
        rotated_vertices = sim.rotate_vertices_around_center(theta_rad)
    
    # 计算特殊点位的旋转位置
    rotated_upper, rotated_lower = sim.rotate_special_points_around_center(
        theta_rad, accum_thickness
    )
    
    # 使用修正的接触点计算
    (original_contact, original_type, 
     corrected_contact, corrected_type, was_corrected) = find_corrected_leftmost_contact_point_90_5(
        sim, rotated_vertices, rotated_upper, test_angle
    )
    
    # 创建对比可视化
    create_90_5_degree_comparison_visualization(
        sim, rotated_vertices, rotated_upper,
        original_contact, original_type,
        corrected_contact, corrected_type, was_corrected
    )
    
    print(f"\n=== 90.5度测试总结 ===")
    print(f"• 原始接触点: {original_type} at ({original_contact[0]:.2f}, {original_contact[1]:.2f})")
    print(f"• 修正接触点: {corrected_type} at ({corrected_contact[0]:.2f}, {corrected_contact[1]:.2f})")
    print(f"• 是否需要修正: {'是' if was_corrected else '否'}")
    if was_corrected:
        print(f"• ⚠️  原始逻辑问题：切线会穿过菱形内部")
        print(f"• ✅ 修正逻辑成功：使用下一个顶点避免穿过内部")
    
    return sim, original_contact, corrected_contact, was_corrected


if __name__ == "__main__":
    test_90_5_degree_correction()
    print(f"\n90.5度切线修正逻辑验证完成！")
