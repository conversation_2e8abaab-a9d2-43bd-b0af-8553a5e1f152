#!/usr/bin/env python3
"""
演示以接触点为中心的角度计算方法
"""

import math

def calculate_angle_from_contact_point():
    """
    演示以接触点为中心计算角度的方法
    """
    print("=== 以接触点为中心的角度计算演示 ===\n")
    
    # 89.5度情况的实际数据
    roller_A = (0.5, 80.0)
    contact_point = (-2.00, 0.03)
    next_vertex_V5 = (-1.84, 18.03)
    
    print(f"过辊A位置: {roller_A}")
    print(f"接触点位置: {contact_point}")
    print(f"下一个顶点V5位置: {next_vertex_V5}")
    print()
    
    # === 原来的计算方法（以过辊A为中心） ===
    print("--- 原来的计算方法（以过辊A为中心） ---")
    
    # 从过辊A指向接触点的向量
    to_contact = (contact_point[0] - roller_A[0], contact_point[1] - roller_A[1])
    angle_old = math.atan2(to_contact[1], to_contact[0])
    angle_old_deg = math.degrees(angle_old)
    
    print(f"从过辊A到接触点的向量: {to_contact}")
    print(f"角度: {angle_old_deg:.2f}°")
    print()
    
    # === 新的计算方法（以接触点为中心） ===
    print("--- 新的计算方法（以接触点为中心） ---")
    
    # 从接触点指向过辊A的向量
    contact_to_roller = (roller_A[0] - contact_point[0], roller_A[1] - contact_point[1])
    tangent_angle = math.atan2(contact_to_roller[1], contact_to_roller[0])
    tangent_angle_deg = math.degrees(tangent_angle)
    
    print(f"从接触点到过辊A的向量: {contact_to_roller}")
    print(f"切线角度（指向过辊A）: {tangent_angle_deg:.2f}°")
    
    # 从接触点指向下一个顶点的向量
    contact_to_vertex = (next_vertex_V5[0] - contact_point[0], next_vertex_V5[1] - contact_point[1])
    vertex_angle = math.atan2(contact_to_vertex[1], contact_to_vertex[0])
    vertex_angle_deg = math.degrees(vertex_angle)
    
    print(f"从接触点到V5的向量: {contact_to_vertex}")
    print(f"指向V5的角度: {vertex_angle_deg:.2f}°")
    print()
    
    # === 角度关系分析 ===
    print("--- 角度关系分析 ---")
    
    angle_diff = vertex_angle_deg - tangent_angle_deg
    
    # 处理角度跨越±180°边界的情况
    if angle_diff > 180:
        angle_diff -= 360
    elif angle_diff < -180:
        angle_diff += 360
    
    print(f"角度差（指向V5 - 指向过辊A）: {angle_diff:.2f}°")
    
    if 0 < angle_diff < 180:
        print("⚠️  切线可能穿过菱形内部！")
        print("   原因：从接触点看，V5在过辊A的逆时针方向")
    else:
        print("✅ 切线角度合理")
    
    print()
    
    # === 几何意义解释 ===
    print("--- 几何意义解释 ---")
    print("以接触点为中心的角度计算更直观：")
    print("1. 切线方向：从接触点指向过辊A")
    print("2. 边界方向：从接触点指向下一个顶点")
    print("3. 如果边界在切线的逆时针方向，切线就会穿过多边形内部")
    print("4. 角度都是正值，更容易理解")

def demonstrate_quadrant_angles():
    """
    演示不同象限的角度计算
    """
    print("\n=== 不同象限的角度演示 ===\n")
    
    contact = (0, 0)  # 以原点为接触点
    
    test_points = [
        ("第一象限", (1, 1)),
        ("第二象限", (-1, 1)),
        ("第三象限", (-1, -1)),
        ("第四象限", (1, -1)),
        ("正X轴", (1, 0)),
        ("正Y轴", (0, 1)),
        ("负X轴", (-1, 0)),
        ("负Y轴", (0, -1))
    ]
    
    for name, point in test_points:
        vector = (point[0] - contact[0], point[1] - contact[1])
        angle = math.atan2(vector[1], vector[0])
        angle_deg = math.degrees(angle)
        print(f"{name:8s}: 点{point} -> 角度 {angle_deg:6.1f}°")

if __name__ == "__main__":
    calculate_angle_from_contact_point()
    demonstrate_quadrant_angles()
