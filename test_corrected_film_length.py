#!/usr/bin/env python3
"""
测试修正后的隔膜长度计算
"""

import numpy as np
import matplotlib.pyplot as plt
from main import HexagonFilmSimulation

def test_film_length_comparison():
    """
    比较修正前后的隔膜长度计算
    """
    print("=== 修正后的隔膜长度计算测试 ===\n")
    
    # 初始化仿真
    sim = HexagonFilmSimulation()
    
    # 测试关键角度点
    test_angles = [85.0, 87.0, 88.0, 88.5, 89.0, 89.5, 90.0, 91.0, 92.0]
    
    print(f"{'角度':<6} {'接触点类型':<20} {'接触点坐标':<15} {'切线长度':<10} {'总长度':<10} {'修正状态':<10}")
    print("-" * 90)
    
    results = []
    
    for angle in test_angles:
        # 计算旋转参数
        theta_rad = np.deg2rad(angle)
        layer_num, accum_thickness = sim.calculate_layer_from_angle(angle)
        
        # 计算旋转后的顶点
        if accum_thickness > 0:
            current_vertices = sim.update_vertices_with_thickness(accum_thickness)
            rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
                theta_rad, current_vertices
            )
        else:
            rotated_vertices = sim.rotate_vertices_around_center(theta_rad)
        
        # 计算特殊点位的旋转位置
        rotated_upper, rotated_lower = sim.rotate_special_points_around_center(
            theta_rad, accum_thickness
        )
        
        # 使用修正后的逻辑找到接触点
        contact, contact_type = sim.find_leftmost_valid_contact_point(
            sim.A, rotated_vertices, rotated_upper, angle
        )
        
        # 计算双过辊系统的各个长度分量
        (
            tangent_AB,
            arc_A,
            arc_B,
            tangent_A,
            contact_A_dynamic,
            contact_A_entry,
            contact_B_exit,
        ) = sim.calculate_roller_to_roller_distance(contact)
        
        # 计算总长度（不包括包覆长度，因为这里只测试单点）
        total_length = tangent_A + tangent_AB + arc_A + arc_B
        
        # 判断是否为修正状态
        is_corrected = "corrected" in contact_type
        correction_status = "已修正" if is_corrected else "原始"
        
        # 格式化输出
        contact_coord = f"({contact[0]:.2f},{contact[1]:.2f})"
        
        print(f"{angle:<6.1f} {contact_type:<20} {contact_coord:<15} {tangent_A:<10.2f} {total_length:<10.2f} {correction_status:<10}")
        
        results.append({
            'angle': angle,
            'contact_type': contact_type,
            'contact': contact,
            'tangent_A': tangent_A,
            'total_length': total_length,
            'is_corrected': is_corrected
        })
    
    return results

def visualize_film_length_changes(results):
    """
    可视化隔膜长度变化
    """
    angles = [r['angle'] for r in results]
    tangent_lengths = [r['tangent_A'] for r in results]
    total_lengths = [r['total_length'] for r in results]
    corrected_flags = [r['is_corrected'] for r in results]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 绘制切线长度变化
    colors = ['red' if corrected else 'blue' for corrected in corrected_flags]
    ax1.scatter(angles, tangent_lengths, c=colors, s=100, alpha=0.7)
    ax1.plot(angles, tangent_lengths, 'k--', alpha=0.5)
    ax1.set_xlabel('旋转角度 (度)')
    ax1.set_ylabel('过辊A到接触点切线长度 (mm)')
    ax1.set_title('修正后的切线长度变化')
    ax1.grid(True, alpha=0.3)
    ax1.legend(['原始算法', '修正算法'], loc='upper right')
    
    # 添加修正点标注
    for i, result in enumerate(results):
        if result['is_corrected']:
            ax1.annotate(f"修正点\n{result['angle']}°", 
                        xy=(result['angle'], result['tangent_A']),
                        xytext=(10, 10), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    # 绘制总长度变化
    ax2.scatter(angles, total_lengths, c=colors, s=100, alpha=0.7)
    ax2.plot(angles, total_lengths, 'k--', alpha=0.5)
    ax2.set_xlabel('旋转角度 (度)')
    ax2.set_ylabel('总薄膜长度 (mm)')
    ax2.set_title('修正后的总薄膜长度变化')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("\n=== 长度变化分析 ===")
    
    # 找到修正前后的长度差异
    corrected_results = [r for r in results if r['is_corrected']]
    if corrected_results:
        print(f"\n修正发生在角度: {[r['angle'] for r in corrected_results]}")
        
        # 找到修正前的最后一个点
        last_original_idx = -1
        for i, r in enumerate(results):
            if not r['is_corrected']:
                last_original_idx = i
            else:
                break
        
        if last_original_idx >= 0 and last_original_idx + 1 < len(results):
            original_result = results[last_original_idx]
            corrected_result = results[last_original_idx + 1]
            
            tangent_diff = corrected_result['tangent_A'] - original_result['tangent_A']
            total_diff = corrected_result['total_length'] - original_result['total_length']
            
            print(f"\n修正前后长度变化:")
            print(f"  切线长度变化: {tangent_diff:+.2f} mm")
            print(f"  总长度变化: {total_diff:+.2f} mm")
            print(f"  修正前角度: {original_result['angle']}° -> 修正后角度: {corrected_result['angle']}°")

def run_full_simulation_test():
    """
    运行完整的仿真测试，检查修正后的算法在整个旋转周期中的表现
    """
    print("\n=== 完整仿真测试 ===")
    
    # 创建仿真实例
    sim = HexagonFilmSimulation()
    
    # 运行仿真（只计算前90度，避免时间过长）
    sim.theta_deg = np.linspace(0, 90, 181)  # 0.5度间隔
    sim.theta = np.deg2rad(sim.theta_deg)
    
    # 初始化数组
    sim.contact_points = np.zeros((len(sim.theta), 2))
    sim.contact_type = [""] * len(sim.theta)
    sim.tangent_A_lengths = np.zeros(len(sim.theta))
    
    print(f"计算 {len(sim.theta)} 个角度点...")
    
    corrected_count = 0
    for i in range(len(sim.theta)):
        angle_deg = sim.theta_deg[i]
        theta_rad = sim.theta[i]
        
        # 计算厚度
        layer_num, accum_thickness = sim.calculate_layer_from_angle(angle_deg)
        
        # 计算旋转后的顶点
        if accum_thickness > 0:
            current_vertices = sim.update_vertices_with_thickness(accum_thickness)
            rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
                theta_rad, current_vertices
            )
        else:
            rotated_vertices = sim.rotate_vertices_around_center(theta_rad)
        
        # 计算特殊点位的旋转位置
        rotated_upper, rotated_lower = sim.rotate_special_points_around_center(
            theta_rad, accum_thickness
        )
        
        # 使用修正后的逻辑找到接触点
        contact, contact_type = sim.find_leftmost_valid_contact_point(
            sim.A, rotated_vertices, rotated_upper, angle_deg
        )
        
        sim.contact_points[i] = contact
        sim.contact_type[i] = contact_type
        
        # 计算切线长度
        tangent_A = np.linalg.norm(contact - sim.A)
        sim.tangent_A_lengths[i] = tangent_A
        
        if "corrected" in contact_type:
            corrected_count += 1
    
    print(f"完成！共有 {corrected_count} 个角度点使用了修正算法")
    print(f"修正比例: {corrected_count / len(sim.theta) * 100:.1f}%")
    
    return sim

if __name__ == "__main__":
    # 测试关键角度点
    results = test_film_length_comparison()
    
    # 可视化结果
    visualize_film_length_changes(results)
    
    # 运行完整仿真测试
    sim = run_full_simulation_test()
