#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用main.py坐标设定的切线分析
确保交叉切线只取过辊A上x>0.5的切点
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class MainCoordinateTangentAnalyzer:
    def __init__(self):
        """使用main.py中的坐标设定初始化"""
        
        # 过辊参数 (来自main.py)
        self.A = np.array([0.5, 80.0])  # 过辊A位置
        self.roller_radius = 2.0  # 过辊半径
        
        # 原始菱形顶点 (来自main.py)
        self.original_vertices = np.array([
            [-30, 0],   # V1 (初始接触点 - "内角")
            [-20, -4],  # V2
            [20, -4],   # V3
            [30, 0],    # V4
            [20, 4],    # V5
            [-20, 4],   # V6
        ])
        
        # 内切圆参数
        self.sharp_radius = 0.8   # 锐角内切圆半径
        self.blunt_radius = 12.0  # 钝角内切圆半径
        
        # 内切圆圆心
        self.circle_centers = np.array([
            [-27.85, 0.00],    # V1 锐角圆心
            [-17.69, 8.00],    # V2 钝角圆心
            [17.69, 8.00],     # V3 钝角圆心
            [27.85, 0.00],     # V4 锐角圆心
            [17.69, -8.00],    # V5 钝角圆心
            [-17.69, -8.00],   # V6 钝角圆心
        ])
        
        # 旋转中心 (来自main.py的计算方式)
        self.geometric_center = np.mean(self.original_vertices, axis=0)
        rotation_center_x_offset = 2.0
        self.rotation_center = np.array([
            self.geometric_center[0] + rotation_center_x_offset,
            self.geometric_center[1],
        ])
        
        print(f"几何中心: {self.geometric_center}")
        print(f"旋转中心: {self.rotation_center}")
        print(f"过辊A位置: {self.A}")
    
    def rotate_point(self, point, angle_deg, center):
        """绕指定中心旋转点"""
        angle_rad = np.deg2rad(angle_deg)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        
        translated = point - center
        rotated = np.array([
            translated[0] * cos_a - translated[1] * sin_a,
            translated[0] * sin_a + translated[1] * cos_a
        ])
        return rotated + center
    
    def calculate_cross_tangent_with_constraint(self, center1, radius1, center2, radius2):
        """计算交叉公切线，确保过辊A上的切点x>0.5"""
        x0, y0 = center1[0], center1[1]
        x1, y1 = center2[0], center2[1]
        R, r = radius1, radius2
        
        d = math.sqrt((x1 - x0)**2 + (y1 - y0)**2)
        
        if d <= abs(R - r) or d < R + r:
            return None
        
        L = d * R / (R + r)
        cos_alpha = (R + r) / d
        alpha = math.acos(cos_alpha)
        
        Hpq = math.atan2(y1 - y0, x1 - x0)
        Hpa = Hpq + alpha
        Hpb = Hpq - alpha
        
        # 计算两条切线的切点
        tangents = []
        
        # 第一条切线
        x2 = x0 + R * math.cos(Hpa)
        y2 = y0 + R * math.sin(Hpa)
        point_A1 = np.array([x2, y2])
        
        x3 = x1 + r * math.cos(Hpa + math.pi)
        y3 = y1 + r * math.sin(Hpa + math.pi)
        point_D1 = np.array([x3, y3])
        
        if point_A1[0] > 0.5:  # 检查x>0.5约束
            length1 = np.linalg.norm(point_D1 - point_A1)
            tangents.append({
                'roller_point': point_A1,
                'diamond_point': point_D1,
                'length': length1,
                'angle': Hpa
            })
        
        # 第二条切线
        x4 = x0 + R * math.cos(Hpb)
        y4 = y0 + R * math.sin(Hpb)
        point_A2 = np.array([x4, y4])
        
        x5 = x1 + r * math.cos(Hpb + math.pi)
        y5 = y1 + r * math.sin(Hpb + math.pi)
        point_D2 = np.array([x5, y5])
        
        if point_A2[0] > 0.5:  # 检查x>0.5约束
            length2 = np.linalg.norm(point_D2 - point_A2)
            tangents.append({
                'roller_point': point_A2,
                'diamond_point': point_D2,
                'length': length2,
                'angle': Hpb
            })
        
        if not tangents:
            return None
        
        return {
            'valid_tangents': tangents,
            'selected': tangents[0],  # 选择第一个有效切线
            'all_calculated': [
                {'roller': point_A1, 'diamond': point_D1, 'valid': point_A1[0] > 0.5},
                {'roller': point_A2, 'diamond': point_D2, 'valid': point_A2[0] > 0.5}
            ]
        }
    
    def visualize_angle_analysis(self, angle_deg):
        """可视化指定角度的切线分析"""
        
        # 旋转内切圆圆心
        rotated_centers = []
        for center in self.circle_centers:
            rotated_center = self.rotate_point(center, angle_deg, self.rotation_center)
            rotated_centers.append(rotated_center)
        
        # 旋转菱形顶点
        rotated_vertices = []
        for vertex in self.original_vertices:
            rotated_vertex = self.rotate_point(vertex, angle_deg, self.rotation_center)
            rotated_vertices.append(rotated_vertex)
        rotated_vertices = np.array(rotated_vertices)
        
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'角度 {angle_deg}° - 交叉切线分析 (x>0.5约束)', fontsize=14, fontweight='bold')
        
        # 绘制过辊A
        roller_circle = Circle(self.A, self.roller_radius, 
                              fill=False, edgecolor='blue', linewidth=3)
        ax.add_patch(roller_circle)
        ax.plot(self.A[0], self.A[1], 'bo', markersize=10, label='过辊A')
        
        # 绘制x=0.5的约束线
        ax.axvline(x=0.5, color='red', linestyle=':', alpha=0.7, label='x=0.5约束线')
        
        # 绘制菱形轮廓
        diamond_poly = Polygon(rotated_vertices, fill=False, edgecolor='gray', 
                              linewidth=1, linestyle='--', alpha=0.5, label='菱形轮廓')
        ax.add_patch(diamond_poly)
        
        # 绘制内切圆和分析切线
        colors = ['red', 'green', 'green', 'red', 'green', 'green']
        labels = ['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)']
        
        for i, (center, color, label) in enumerate(zip(rotated_centers, colors, labels)):
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius
            
            # 绘制内切圆
            circle = Circle(center, radius, fill=False, edgecolor=color, linewidth=2, alpha=0.7)
            ax.add_patch(circle)
            ax.plot(center[0], center[1], 'o', color=color, markersize=6)
            ax.text(center[0]+2, center[1]+2, label, fontsize=10, color=color, fontweight='bold')
            
            # 计算交叉切线
            cross_result = self.calculate_cross_tangent_with_constraint(
                self.A, self.roller_radius, center, radius)
            
            if cross_result:
                selected = cross_result['selected']
                
                # 绘制选中的切线
                ax.plot([selected['roller_point'][0], selected['diamond_point'][0]], 
                       [selected['roller_point'][1], selected['diamond_point'][1]], 
                       color=color, linewidth=3, alpha=0.8,
                       label=f'{label} 切线 (L={selected["length"]:.1f}mm)')
                
                # 标注切点
                ax.plot(selected['roller_point'][0], selected['roller_point'][1], 
                       'o', color=color, markersize=8)
                ax.plot(selected['diamond_point'][0], selected['diamond_point'][1], 
                       'o', color=color, markersize=8)
                
                # 标注过辊A切点的x坐标
                ax.text(selected['roller_point'][0]+1, selected['roller_point'][1]+3, 
                       f'x={selected["roller_point"][0]:.2f}', 
                       fontsize=9, color=color, fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
                
                # 显示所有计算的切点信息
                all_calc = cross_result['all_calculated']
                print(f"\n{label} (角度{angle_deg}°):")
                print(f"  圆心: ({center[0]:.2f}, {center[1]:.2f})")
                for j, calc in enumerate(all_calc):
                    status = "✓有效" if calc['valid'] else "✗无效"
                    print(f"  切线{j+1}: 过辊A切点x={calc['roller'][0]:.3f} {status}")
        
        # 设置坐标轴范围
        ax.set_xlim(-40, 40)
        ax.set_ylim(-20, 90)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.show()


def test_main_coordinate_analysis():
    """测试使用main.py坐标的切线分析"""
    
    analyzer = MainCoordinateTangentAnalyzer()
    
    # 测试几个关键角度
    test_angles = [0, 30, 60, 90, 120, 150]
    
    for angle in test_angles:
        print(f"\n{'='*60}")
        print(f"分析角度: {angle}°")
        print(f"{'='*60}")
        analyzer.visualize_angle_analysis(angle)


if __name__ == "__main__":
    print("使用main.py坐标设定的切线分析")
    print("确保交叉切线只取过辊A上x>0.5的切点")
    test_main_coordinate_analysis()
    print("\n分析完成！")
