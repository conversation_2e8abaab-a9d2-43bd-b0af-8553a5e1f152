#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按照main.py最左侧点逻辑确定接触圆弧的交叉公切线分析
只计算交叉公切线，确保过辊A上x>0.5的切点
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class LeftmostCircleTangentAnalyzer:
    def __init__(self):
        """使用main.py的坐标和最左侧点逻辑"""
        
        # 过辊参数 (来自main.py)
        self.A = np.array([0.5, 80.0])  # 过辊A位置
        self.roller_radius = 2.0  # 过辊半径
        
        # 原始菱形顶点 (来自main.py)
        self.original_vertices = np.array([
            [-30, 0],   # V1 (初始接触点 - "内角")
            [-20, -4],  # V2
            [20, -4],   # V3
            [30, 0],    # V4
            [20, 4],    # V5
            [-20, 4],   # V6
        ])
        
        # 内切圆参数
        self.sharp_radius = 0.8   # 锐角内切圆半径 (V1, V4)
        self.blunt_radius = 12.0  # 钝角内切圆半径 (V2, V3, V5, V6)
        
        # 内切圆圆心
        self.circle_centers = np.array([
            [-27.85, 0.00],    # V1 锐角圆心
            [-17.69, 8.00],    # V2 钝角圆心
            [17.69, 8.00],     # V3 钝角圆心
            [27.85, 0.00],     # V4 锐角圆心
            [17.69, -8.00],    # V5 钝角圆心
            [-17.69, -8.00],   # V6 钝角圆心
        ])
        
        # 旋转中心 (来自main.py的计算方式)
        self.geometric_center = np.mean(self.original_vertices, axis=0)
        rotation_center_x_offset = 2.0
        self.rotation_center = np.array([
            self.geometric_center[0] + rotation_center_x_offset,
            self.geometric_center[1],
        ])
        
        # 上方特殊点 (来自main.py)
        self.upper_point = np.array([
            self.geometric_center[0] + rotation_center_x_offset, 4
        ])
        
        print(f"几何中心: {self.geometric_center}")
        print(f"旋转中心: {self.rotation_center}")
        print(f"过辊A位置: {self.A}")
        print(f"上方特殊点: {self.upper_point}")
    
    def rotate_point(self, point, angle_deg, center):
        """绕指定中心旋转点"""
        angle_rad = np.deg2rad(angle_deg)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        
        translated = point - center
        rotated = np.array([
            translated[0] * cos_a - translated[1] * sin_a,
            translated[0] * sin_a + translated[1] * cos_a
        ])
        return rotated + center
    
    def find_leftmost_contact_point(self, rotated_vertices, rotated_upper, angle_deg):
        """
        按照main.py的逻辑寻找最左侧接触点
        关键约束：180度之前只考虑V3、V4、V5，不包括V1、V2、V6
        """
        # 初始状态：固定连接到上方点，保持到接近90度
        if angle_deg < 85:
            return rotated_upper, "fixed_upper_point", None

        # 候选点信息
        candidate_info = []

        if angle_deg < 180:
            # 旋转小于180度：只考虑V3、V4、V5（索引2、3、4）+ (2,4)点
            valid_vertex_indices = [2, 3, 4]  # V3, V4, V5

            # 添加有效的六边形顶点
            for idx in valid_vertex_indices:
                vertex = rotated_vertices[idx]
                candidate_info.append({
                    "point": vertex,
                    "type": f"vertex_V{idx + 1}",
                    "x": vertex[0],
                    "circle_index": idx
                })

            # 添加上方点
            candidate_info.append({
                "point": rotated_upper,
                "type": "upper_point",
                "x": rotated_upper[0],
                "circle_index": None
            })

        else:
            # 旋转超过180度：考虑所有顶点
            for idx, vertex in enumerate(rotated_vertices):
                candidate_info.append({
                    "point": vertex,
                    "type": f"vertex_V{idx + 1}",
                    "x": vertex[0],
                    "circle_index": idx
                })

            # 也添加上方点
            candidate_info.append({
                "point": rotated_upper,
                "type": "upper_point",
                "x": rotated_upper[0],
                "circle_index": None
            })

        # 在候选点中寻找最左侧的点（X坐标最小）
        if not candidate_info:
            return rotated_upper, "upper_point", None

        leftmost_candidate = min(candidate_info, key=lambda x: x["x"])
        leftmost_point = leftmost_candidate["point"]
        leftmost_type = leftmost_candidate["type"]
        circle_index = leftmost_candidate["circle_index"]

        # 返回最左侧的点
        if leftmost_type == "upper_point":
            return leftmost_point, "leftmost_upper_point", None
        elif leftmost_type.startswith("vertex_"):
            vertex_name = leftmost_type.replace("vertex_", "")
            return leftmost_point, f"leftmost_{vertex_name}", circle_index
        else:
            return leftmost_point, leftmost_type, circle_index
    
    def calculate_cross_tangent_with_constraint(self, center1, radius1, center2, radius2):
        """计算交叉公切线，确保过辊A上的切点x>0.5"""
        x0, y0 = center1[0], center1[1]
        x1, y1 = center2[0], center2[1]
        R, r = radius1, radius2
        
        d = math.sqrt((x1 - x0)**2 + (y1 - y0)**2)
        
        if d <= abs(R - r) or d < R + r:
            return None
        
        L = d * R / (R + r)
        cos_alpha = (R + r) / d
        alpha = math.acos(cos_alpha)
        
        Hpq = math.atan2(y1 - y0, x1 - x0)
        Hpa = Hpq + alpha
        Hpb = Hpq - alpha
        
        # 计算两条切线的切点
        tangents = []
        
        # 第一条切线
        x2 = x0 + R * math.cos(Hpa)
        y2 = y0 + R * math.sin(Hpa)
        point_A1 = np.array([x2, y2])
        
        x3 = x1 + r * math.cos(Hpa + math.pi)
        y3 = y1 + r * math.sin(Hpa + math.pi)
        point_D1 = np.array([x3, y3])
        
        if point_A1[0] > 0.5:  # 检查x>0.5约束
            length1 = np.linalg.norm(point_D1 - point_A1)
            tangents.append({
                'roller_point': point_A1,
                'diamond_point': point_D1,
                'length': length1,
                'angle': Hpa
            })
        
        # 第二条切线
        x4 = x0 + R * math.cos(Hpb)
        y4 = y0 + R * math.sin(Hpb)
        point_A2 = np.array([x4, y4])
        
        x5 = x1 + r * math.cos(Hpb + math.pi)
        y5 = y1 + r * math.sin(Hpb + math.pi)
        point_D2 = np.array([x5, y5])
        
        if point_A2[0] > 0.5:  # 检查x>0.5约束
            length2 = np.linalg.norm(point_D2 - point_A2)
            tangents.append({
                'roller_point': point_A2,
                'diamond_point': point_D2,
                'length': length2,
                'angle': Hpb
            })
        
        if not tangents:
            return None
        
        return {
            'valid_tangents': tangents,
            'selected': tangents[0],  # 选择第一个有效切线
        }
    
    def analyze_angle(self, angle_deg):
        """分析指定角度下的接触情况"""
        
        # 旋转顶点和上方点
        rotated_vertices = []
        for vertex in self.original_vertices:
            rotated_vertex = self.rotate_point(vertex, angle_deg, self.rotation_center)
            rotated_vertices.append(rotated_vertex)
        rotated_vertices = np.array(rotated_vertices)
        
        rotated_upper = self.rotate_point(self.upper_point, angle_deg, self.rotation_center)
        
        # 找到最左侧接触点
        contact_point, contact_type, circle_index = self.find_leftmost_contact_point(
            rotated_vertices, rotated_upper, angle_deg)
        
        # 如果接触点对应某个顶点，计算对应内切圆的交叉切线
        tangent_result = None
        if circle_index is not None:
            # 旋转对应的内切圆圆心
            rotated_center = self.rotate_point(
                self.circle_centers[circle_index], angle_deg, self.rotation_center)
            
            # 确定半径
            radius = self.sharp_radius if circle_index in [0, 3] else self.blunt_radius
            
            # 计算交叉切线
            tangent_result = self.calculate_cross_tangent_with_constraint(
                self.A, self.roller_radius, rotated_center, radius)
        
        return {
            'contact_point': contact_point,
            'contact_type': contact_type,
            'circle_index': circle_index,
            'rotated_vertices': rotated_vertices,
            'rotated_upper': rotated_upper,
            'tangent_result': tangent_result,
            'rotated_centers': [self.rotate_point(center, angle_deg, self.rotation_center) 
                               for center in self.circle_centers]
        }
    
    def visualize_analysis(self, angle_deg):
        """可视化分析结果"""
        
        result = self.analyze_angle(angle_deg)
        
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'角度 {angle_deg}° - 最左侧点接触圆弧分析', fontsize=14, fontweight='bold')
        
        # 绘制过辊A
        roller_circle = Circle(self.A, self.roller_radius, 
                              fill=False, edgecolor='blue', linewidth=3)
        ax.add_patch(roller_circle)
        ax.plot(self.A[0], self.A[1], 'bo', markersize=10, label='过辊A')
        
        # 绘制x=0.5的约束线
        ax.axvline(x=0.5, color='red', linestyle=':', alpha=0.7, label='x=0.5约束线')
        
        # 绘制菱形轮廓
        diamond_poly = Polygon(result['rotated_vertices'], fill=False, edgecolor='gray', 
                              linewidth=1, linestyle='--', alpha=0.5, label='菱形轮廓')
        ax.add_patch(diamond_poly)
        
        # 绘制所有内切圆
        colors = ['red', 'green', 'green', 'red', 'green', 'green']
        labels = ['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)']
        
        for i, (center, color, label) in enumerate(zip(result['rotated_centers'], colors, labels)):
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius
            
            # 高亮当前接触的圆
            alpha = 1.0 if i == result['circle_index'] else 0.3
            linewidth = 3 if i == result['circle_index'] else 1
            
            circle = Circle(center, radius, fill=False, edgecolor=color, 
                           linewidth=linewidth, alpha=alpha)
            ax.add_patch(circle)
            ax.plot(center[0], center[1], 'o', color=color, markersize=6, alpha=alpha)
            
            if i == result['circle_index']:
                ax.text(center[0]+2, center[1]+2, f'{label} ★接触中★', 
                       fontsize=12, color=color, fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8))
            else:
                ax.text(center[0]+1, center[1]+1, label, fontsize=9, color=color, alpha=0.7)
        
        # 绘制接触点
        contact_point = result['contact_point']
        ax.plot(contact_point[0], contact_point[1], 'mo', markersize=12, 
               markerfacecolor='magenta', markeredgecolor='darkmagenta', 
               markeredgewidth=2, label='最左侧接触点')
        
        # 绘制上方特殊点
        ax.plot(result['rotated_upper'][0], result['rotated_upper'][1], 'co', 
               markersize=8, label='上方特殊点')
        
        # 绘制交叉切线（如果存在）
        if result['tangent_result']:
            tangent = result['tangent_result']['selected']
            
            ax.plot([tangent['roller_point'][0], tangent['diamond_point'][0]], 
                   [tangent['roller_point'][1], tangent['diamond_point'][1]], 
                   'orange', linewidth=4, alpha=0.9,
                   label=f'交叉公切线 (L={tangent["length"]:.1f}mm)')
            
            # 标注切点
            ax.plot(tangent['roller_point'][0], tangent['roller_point'][1], 
                   'o', color='orange', markersize=10)
            ax.plot(tangent['diamond_point'][0], tangent['diamond_point'][1], 
                   'o', color='orange', markersize=10)
            
            # 标注过辊A切点的x坐标
            ax.text(tangent['roller_point'][0]+1, tangent['roller_point'][1]+3, 
                   f'x={tangent["roller_point"][0]:.2f}', 
                   fontsize=10, color='orange', fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
        
        # 标注最左侧点的x坐标
        all_x = [v[0] for v in result['rotated_vertices']] + [result['rotated_upper'][0]]
        min_x = min(all_x)
        ax.axvline(x=min_x, color='green', linestyle='--', alpha=0.7, 
                  label=f'最左侧X={min_x:.2f}')
        
        ax.set_xlim(-40, 40)
        ax.set_ylim(-20, 90)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.show()
        
        # 打印分析结果
        print(f"\n=== 角度 {angle_deg}° 分析结果 ===")
        print(f"接触类型: {result['contact_type']}")
        print(f"接触点坐标: ({contact_point[0]:.2f}, {contact_point[1]:.2f})")
        
        if result['circle_index'] is not None:
            circle_label = labels[result['circle_index']]
            print(f"接触圆弧: {circle_label}")
            
            if result['tangent_result']:
                tangent = result['tangent_result']['selected']
                print(f"交叉切线长度: {tangent['length']:.2f}mm")
                print(f"过辊A切点: ({tangent['roller_point'][0]:.3f}, {tangent['roller_point'][1]:.3f})")
                print(f"内切圆切点: ({tangent['diamond_point'][0]:.3f}, {tangent['diamond_point'][1]:.3f})")
                print(f"过辊A切点x坐标: {tangent['roller_point'][0]:.3f} (>0.5: {tangent['roller_point'][0] > 0.5})")
            else:
                print("无有效交叉切线 (不满足x>0.5约束)")
        else:
            print("当前接触上方特殊点，无对应内切圆")


def test_leftmost_analysis():
    """测试最左侧点接触圆弧分析"""
    
    analyzer = LeftmostCircleTangentAnalyzer()
    
    # 测试关键角度
    test_angles = [0, 30, 60, 90, 120, 150, 180]
    
    for angle in test_angles:
        print(f"\n{'='*60}")
        print(f"分析角度: {angle}°")
        print(f"{'='*60}")
        analyzer.visualize_analysis(angle)


if __name__ == "__main__":
    print("按照main.py最左侧点逻辑的接触圆弧交叉公切线分析")
    print("只计算交叉公切线，确保过辊A上x>0.5的切点")
    test_leftmost_analysis()
    print("\n分析完成！")
