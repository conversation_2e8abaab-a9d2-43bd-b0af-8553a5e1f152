#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交叉公切线（内公切线）计算
根据用户提供的公式实现两圆交叉公切线的计算
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class CrossTangentCalculator:
    def __init__(self):
        """初始化交叉公切线计算器"""
        pass
    
    def calculate_cross_tangent(self, center1, radius1, center2, radius2):
        """
        计算两圆的交叉公切线（内公切线）
        
        参数:
        center1: 第一个圆的圆心 [x0, y0]
        radius1: 第一个圆的半径 R
        center2: 第二个圆的圆心 [x1, y1] 
        radius2: 第二个圆的半径 r
        
        返回:
        字典包含两条切线的信息
        """
        x0, y0 = center1[0], center1[1]
        x1, y1 = center2[0], center2[1]
        R = radius1
        r = radius2
        
        # 计算圆心距离 d = |PQ|
        d = math.sqrt((x1 - x0)**2 + (y1 - y0)**2)
        
        # 检查是否可以有交叉公切线
        if d <= abs(R - r):
            return None  # 一个圆在另一个圆内部，无交叉公切线
        
        if d < R + r:
            return None  # 两圆相交，无交叉公切线
        
        # 根据公式计算 L = dR / (R + r)
        L = d * R / (R + r)
        
        # 计算 α = acos((R + r) / d)
        cos_alpha = (R + r) / d
        alpha = math.acos(cos_alpha)
        
        # 计算 Hpq = atan((y1-y0)/(x1-x0))
        Hpq = math.atan2(y1 - y0, x1 - x0)
        
        # 计算两条切线的角度
        Hpa = Hpq + alpha  # 第一条切线
        Hpb = Hpq - alpha  # 第二条切线
        
        # 计算切线方向角度
        Had = Hpa - math.pi/2  # 第一条切线的方向
        Hbc = Hpb + math.pi/2  # 第二条切线的方向
        
        # 计算切线斜率
        k1 = math.tan(Had)
        k2 = math.tan(Hbc)
        
        # 计算第一条切线的切点
        # 点A (第一个圆上的切点)
        x2 = x0 + R * math.cos(Hpa)
        y2 = y0 + R * math.sin(Hpa)
        point_A = np.array([x2, y2])
        
        # 点D (第二个圆上的切点)
        x3 = x1 + r * math.cos(Hpa + math.pi)
        y3 = y1 + r * math.sin(Hpa + math.pi)
        point_D = np.array([x3, y3])
        
        # 计算第二条切线的切点
        # 点B (第一个圆上的切点)
        x4 = x0 + R * math.cos(Hpb)
        y4 = y0 + R * math.sin(Hpb)
        point_B = np.array([x4, y4])
        
        # 点C (第二个圆上的切点)
        x5 = x1 + r * math.cos(Hpb + math.pi)
        y5 = y1 + r * math.sin(Hpb + math.pi)
        point_C = np.array([x5, y5])
        
        # 计算切线长度
        tangent_length1 = np.linalg.norm(point_D - point_A)
        tangent_length2 = np.linalg.norm(point_C - point_B)
        
        return {
            'tangent1': {
                'point_on_circle1': point_A,
                'point_on_circle2': point_D,
                'slope': k1,
                'length': tangent_length1,
                'angle': Had
            },
            'tangent2': {
                'point_on_circle1': point_B,
                'point_on_circle2': point_C,
                'slope': k2,
                'length': tangent_length2,
                'angle': Hbc
            },
            'parameters': {
                'd': d,
                'L': L,
                'alpha': alpha,
                'Hpq': Hpq,
                'Hpa': Hpa,
                'Hpb': Hpb
            }
        }
    
    def get_line_equation(self, point, slope):
        """根据点和斜率计算直线方程 y = mx + b"""
        # y - y1 = m(x - x1)  =>  y = mx - mx1 + y1
        b = point[1] - slope * point[0]
        return slope, b
    
    def visualize_cross_tangent(self, center1, radius1, center2, radius2):
        """可视化交叉公切线"""
        
        result = self.calculate_cross_tangent(center1, radius1, center2, radius2)
        
        if result is None:
            print("无法计算交叉公切线（圆相交或包含）")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        
        # 绘制两个圆
        circle1 = Circle(center1, radius1, fill=False, edgecolor='blue', linewidth=2, label=f'圆1 (R={radius1})')
        circle2 = Circle(center2, radius2, fill=False, edgecolor='red', linewidth=2, label=f'圆2 (r={radius2})')
        ax.add_patch(circle1)
        ax.add_patch(circle2)
        
        # 标注圆心
        ax.plot(center1[0], center1[1], 'bo', markersize=8)
        ax.plot(center2[0], center2[1], 'ro', markersize=8)
        ax.text(center1[0]+1, center1[1]+1, 'P(圆1中心)', fontsize=10, fontweight='bold')
        ax.text(center2[0]+1, center2[1]+1, 'Q(圆2中心)', fontsize=10, fontweight='bold')
        
        # 绘制连心线
        ax.plot([center1[0], center2[0]], [center1[1], center2[1]], 'k--', alpha=0.5, label='连心线PQ')
        
        # 绘制第一条交叉公切线
        t1 = result['tangent1']
        ax.plot([t1['point_on_circle1'][0], t1['point_on_circle2'][0]], 
               [t1['point_on_circle1'][1], t1['point_on_circle2'][1]], 
               'g-', linewidth=3, alpha=0.8, label=f'交叉公切线1 (L={t1["length"]:.2f})')
        
        # 标注第一条切线的切点
        ax.plot(t1['point_on_circle1'][0], t1['point_on_circle1'][1], 'go', markersize=8)
        ax.plot(t1['point_on_circle2'][0], t1['point_on_circle2'][1], 'go', markersize=8)
        ax.text(t1['point_on_circle1'][0]+1, t1['point_on_circle1'][1]+1, 'A', fontsize=12, fontweight='bold')
        ax.text(t1['point_on_circle2'][0]+1, t1['point_on_circle2'][1]+1, 'D', fontsize=12, fontweight='bold')
        
        # 绘制第二条交叉公切线
        t2 = result['tangent2']
        ax.plot([t2['point_on_circle1'][0], t2['point_on_circle2'][0]], 
               [t2['point_on_circle1'][1], t2['point_on_circle2'][1]], 
               'orange', linewidth=3, alpha=0.8, label=f'交叉公切线2 (L={t2["length"]:.2f})')
        
        # 标注第二条切线的切点
        ax.plot(t2['point_on_circle1'][0], t2['point_on_circle1'][1], 'o', color='orange', markersize=8)
        ax.plot(t2['point_on_circle2'][0], t2['point_on_circle2'][1], 'o', color='orange', markersize=8)
        ax.text(t2['point_on_circle1'][0]+1, t2['point_on_circle1'][1]+1, 'B', fontsize=12, fontweight='bold')
        ax.text(t2['point_on_circle2'][0]+1, t2['point_on_circle2'][1]+1, 'C', fontsize=12, fontweight='bold')
        
        # 设置坐标轴范围
        all_x = [center1[0], center2[0], t1['point_on_circle1'][0], t1['point_on_circle2'][0], 
                t2['point_on_circle1'][0], t2['point_on_circle2'][0]]
        all_y = [center1[1], center2[1], t1['point_on_circle1'][1], t1['point_on_circle2'][1], 
                t2['point_on_circle1'][1], t2['point_on_circle2'][1]]
        
        margin = max(radius1, radius2) + 5
        ax.set_xlim(min(all_x) - margin, max(all_x) + margin)
        ax.set_ylim(min(all_y) - margin, max(all_y) + margin)
        
        ax.legend()
        ax.set_title('两圆交叉公切线（内公切线）计算', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        plt.show()
        
        # 打印计算详情
        params = result['parameters']
        print(f"\n=== 交叉公切线计算详情 ===")
        print(f"圆1中心: P({center1[0]}, {center1[1]}), 半径R = {radius1}")
        print(f"圆2中心: Q({center2[0]}, {center2[1]}), 半径r = {radius2}")
        print(f"圆心距离: d = |PQ| = {params['d']:.3f}")
        print(f"计算参数 L = dR/(R+r) = {params['L']:.3f}")
        print(f"角度 α = acos((R+r)/d) = {math.degrees(params['alpha']):.2f}°")
        print(f"连心线角度 Hpq = {math.degrees(params['Hpq']):.2f}°")
        print(f"切线角度 Hpa = {math.degrees(params['Hpa']):.2f}°")
        print(f"切线角度 Hpb = {math.degrees(params['Hpb']):.2f}°")
        
        print(f"\n第一条切线:")
        print(f"  切点A: ({t1['point_on_circle1'][0]:.3f}, {t1['point_on_circle1'][1]:.3f})")
        print(f"  切点D: ({t1['point_on_circle2'][0]:.3f}, {t1['point_on_circle2'][1]:.3f})")
        print(f"  斜率k1: {t1['slope']:.3f}")
        print(f"  长度: {t1['length']:.3f}")
        
        print(f"\n第二条切线:")
        print(f"  切点B: ({t2['point_on_circle1'][0]:.3f}, {t2['point_on_circle1'][1]:.3f})")
        print(f"  切点C: ({t2['point_on_circle2'][0]:.3f}, {t2['point_on_circle2'][1]:.3f})")
        print(f"  斜率k2: {t2['slope']:.3f}")
        print(f"  长度: {t2['length']:.3f}")


def test_cross_tangent():
    """测试交叉公切线计算"""
    
    calculator = CrossTangentCalculator()
    
    # 测试用例1：两个分离的圆
    print("=== 测试用例1：两个分离的圆 ===")
    center1 = np.array([0, 0])
    radius1 = 3
    center2 = np.array([10, 0])
    radius2 = 2
    
    calculator.visualize_cross_tangent(center1, radius1, center2, radius2)
    
    # 测试用例2：不同位置的两个圆
    print("\n=== 测试用例2：不同位置的两个圆 ===")
    center1 = np.array([-5, 3])
    radius1 = 4
    center2 = np.array([8, -2])
    radius2 = 3
    
    calculator.visualize_cross_tangent(center1, radius1, center2, radius2)
    
    # 测试用例3：过辊A和内切圆的情况
    print("\n=== 测试用例3：过辊A和内切圆 ===")
    roller_center = np.array([0.5, 80.0])
    roller_radius = 2.0
    diamond_center = np.array([-27.85, 0.0])
    diamond_radius = 0.8
    
    calculator.visualize_cross_tangent(roller_center, roller_radius, diamond_center, diamond_radius)


if __name__ == "__main__":
    print("交叉公切线（内公切线）计算测试")
    test_cross_tangent()
    print("\n测试完成！")
