#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：生成89.5度切线与菱形卷正状态的图像
测试当前main.py中的逻辑是否正确
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from main import HexagonFilmSimulation

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


def test_89_5_degree_tangent():
    """
    测试89.5度时的切线与菱形卷正状态
    """
    print("开始测试89.5度切线与菱形卷正状态...")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0, film_thickness=0.1)
    
    # 测试角度：89.5度
    test_angle = 89.5
    theta_rad = np.deg2rad(test_angle)
    
    # 计算当前角度对应的厚度
    layer_num, accum_thickness = sim.calculate_layer_from_angle(test_angle)
    print(f"测试角度: {test_angle}°")
    print(f"层数: {layer_num}, 累积厚度: {accum_thickness:.3f} mm")
    
    # 根据厚度更新顶点
    if accum_thickness > 0:
        current_vertices = sim.update_vertices_with_thickness(accum_thickness)
        rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
            theta_rad, current_vertices
        )
    else:
        rotated_vertices = sim.rotate_vertices_around_center(theta_rad)
    
    # 计算特殊点位的旋转位置
    rotated_upper, rotated_lower = sim.rotate_special_points_around_center(
        theta_rad, accum_thickness
    )
    
    # 使用最左侧点包覆规则计算接触点
    contact, contact_type = sim.find_leftmost_valid_contact_point(
        sim.A, rotated_vertices, rotated_upper, test_angle
    )
    
    # 计算过辊接触点
    roller_contact = sim.find_roller_contact_point(contact)
    
    # 计算双过辊系统的各个长度分量
    (
        tangent_AB,
        arc_A,
        arc_B,
        tangent_A,
        contact_A_dynamic,
        contact_A_entry,
        contact_B_exit,
    ) = sim.calculate_roller_to_roller_distance(contact)
    
    print(f"\n接触点分析结果:")
    print(f"接触点类型: {contact_type}")
    print(f"接触点坐标: ({contact[0]:.2f}, {contact[1]:.2f})")
    print(f"过辊A动态切点: ({contact_A_dynamic[0]:.2f}, {contact_A_dynamic[1]:.2f})")
    print(f"过辊A-B切线接触点: ({contact_A_entry[0]:.2f}, {contact_A_entry[1]:.2f})")
    print(f"过辊B出线点: ({contact_B_exit[0]:.2f}, {contact_B_exit[1]:.2f})")
    print(f"\n长度分量:")
    print(f"过辊A到接触点切线长度: {tangent_A:.2f} mm")
    print(f"过辊A-B切线长度: {tangent_AB:.2f} mm")
    print(f"过辊A圆弧长度: {arc_A:.2f} mm")
    print(f"过辊B圆弧长度: {arc_B:.2f} mm")
    
    # 创建可视化图像
    create_89_5_degree_visualization(
        sim, test_angle, rotated_vertices, rotated_upper, rotated_lower,
        contact, contact_type, roller_contact, contact_A_dynamic,
        contact_A_entry, contact_B_exit, tangent_A, tangent_AB, arc_A, arc_B
    )
    
    return sim, contact, contact_type, tangent_A


def create_89_5_degree_visualization(
    sim, angle, rotated_vertices, rotated_upper, rotated_lower,
    contact, contact_type, roller_contact, contact_A_dynamic,
    contact_A_entry, contact_B_exit, tangent_A, tangent_AB, arc_A, arc_B
):
    """
    创建89.5度状态的详细可视化图像
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # 图1: 整体系统布局
    ax1.set_aspect("equal")
    ax1.grid(True, alpha=0.3)
    ax1.set_title(f"89.5度切线与菱形卷正状态 - 整体布局", fontsize=14, fontweight="bold")
    
    # 绘制过辊A和B
    ax1.plot(sim.A[0], sim.A[1], "bo", markersize=15, label="过辊A")
    ax1.plot(sim.B[0], sim.B[1], "co", markersize=15, label="过辊B")
    
    # 绘制过辊圆
    roller_A_circle = plt.Circle(sim.A, sim.roller_radius, fill=False, color="blue", linewidth=2)
    roller_B_circle = plt.Circle(sim.B, sim.roller_radius, fill=False, color="cyan", linewidth=2)
    ax1.add_patch(roller_A_circle)
    ax1.add_patch(roller_B_circle)
    
    # 绘制旋转中心和几何中心
    ax1.plot(sim.geometric_center[0], sim.geometric_center[1], "gs", markersize=12, label="几何中心")
    ax1.plot(sim.rotation_center[0], sim.rotation_center[1], "r^", markersize=12, label="旋转中心")
    
    # 绘制六边形（菱形卷正状态）
    hex_x = np.append(rotated_vertices[:, 0], rotated_vertices[0, 0])
    hex_y = np.append(rotated_vertices[:, 1], rotated_vertices[0, 1])
    ax1.plot(hex_x, hex_y, "k-", linewidth=3, label="六边形卷针")
    
    # 标注顶点
    for i, vertex in enumerate(rotated_vertices):
        ax1.plot(vertex[0], vertex[1], "ko", markersize=8)
        ax1.text(vertex[0] + 1, vertex[1] + 1, f"V{i + 1}", fontsize=10, fontweight="bold")
    
    # 绘制特殊点位
    ax1.plot(rotated_upper[0], rotated_upper[1], "mo", markersize=12, label="上方点(2,4)")
    ax1.plot(rotated_lower[0], rotated_lower[1], "co", markersize=12, label="下方点(-2,-4)")
    
    # 绘制薄膜路径系统
    # 1. 过辊B到过辊A的切线
    ax1.plot([contact_B_exit[0], contact_A_entry[0]], [contact_B_exit[1], contact_A_entry[1]], 
             "purple", linewidth=4, alpha=0.8, label=f"过辊B-A切线 ({tangent_AB:.1f}mm)")
    
    # 2. 过辊A上的圆弧（简化显示）
    if arc_A > 0:
        arc_angles = np.linspace(0, arc_A/sim.roller_radius, 20)
        arc_x = sim.A[0] + sim.roller_radius * np.cos(arc_angles + np.pi/2)
        arc_y = sim.A[1] + sim.roller_radius * np.sin(arc_angles + np.pi/2)
        ax1.plot(arc_x, arc_y, "orange", linewidth=4, alpha=0.8, label=f"过辊A圆弧 ({arc_A:.1f}mm)")
    
    # 3. 过辊A到接触点的切线
    ax1.plot([contact_A_dynamic[0], contact[0]], [contact_A_dynamic[1], contact[1]], 
             "red", linewidth=4, alpha=0.9, label=f"过辊A-接触点切线 ({tangent_A:.1f}mm)")
    
    # 标注关键点
    ax1.plot(contact[0], contact[1], "ro", markersize=12, label="薄膜接触点")
    ax1.plot(contact_A_dynamic[0], contact_A_dynamic[1], "go", markersize=10, label="过辊A动态切点")
    ax1.plot(contact_A_entry[0], contact_A_entry[1], "yo", markersize=10, label="过辊A-B切线点")
    ax1.plot(contact_B_exit[0], contact_B_exit[1], "mo", markersize=10, label="过辊B出线点")
    
    ax1.set_xlim(-40, 40)
    ax1.set_ylim(-15, 90)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 图2: 切线几何分析
    ax2.set_aspect("equal")
    ax2.grid(True, alpha=0.3)
    ax2.set_title(f"89.5度切线几何分析", fontsize=14, fontweight="bold")
    
    # 放大显示过辊A区域的切线几何
    ax2.plot(sim.A[0], sim.A[1], "bo", markersize=15, label="过辊A中心")
    roller_circle = plt.Circle(sim.A, sim.roller_radius, fill=False, color="blue", linewidth=2)
    ax2.add_patch(roller_circle)
    
    # 绘制切线
    ax2.plot([contact_A_dynamic[0], contact[0]], [contact_A_dynamic[1], contact[1]], 
             "red", linewidth=3, label=f"切线长度: {tangent_A:.2f}mm")
    
    # 绘制关键点
    ax2.plot(contact[0], contact[1], "ro", markersize=12, label="接触点")
    ax2.plot(contact_A_dynamic[0], contact_A_dynamic[1], "go", markersize=10, label="切点")
    
    # 绘制切线角度
    center_to_contact = contact - sim.A
    center_to_tangent = contact_A_dynamic - sim.A
    angle_between = np.arccos(np.clip(np.dot(center_to_contact, center_to_tangent) / 
                                     (np.linalg.norm(center_to_contact) * np.linalg.norm(center_to_tangent)), -1, 1))
    
    ax2.text(sim.A[0] + 5, sim.A[1] + 5, f"切线角度: {np.rad2deg(angle_between):.1f}°", 
             fontsize=12, bbox=dict(boxstyle="round", facecolor="yellow", alpha=0.8))
    
    ax2.set_xlim(sim.A[0] - 10, contact[0] + 10)
    ax2.set_ylim(min(sim.A[1], contact[1]) - 10, max(sim.A[1], contact[1]) + 10)
    ax2.legend()
    
    # 图3: 菱形状态分析
    ax3.set_aspect("equal")
    ax3.grid(True, alpha=0.3)
    ax3.set_title(f"菱形卷正状态分析 (角度: {angle}°)", fontsize=14, fontweight="bold")
    
    # 绘制六边形的变形状态
    hex_polygon = patches.Polygon(rotated_vertices, closed=True, fill=False, 
                                 edgecolor="black", linewidth=3, label="六边形轮廓")
    ax3.add_patch(hex_polygon)
    
    # 标注各边长度
    for i in range(len(rotated_vertices)):
        v1 = rotated_vertices[i]
        v2 = rotated_vertices[(i + 1) % len(rotated_vertices)]
        edge_length = np.linalg.norm(v2 - v1)
        mid_point = (v1 + v2) / 2
        ax3.text(mid_point[0], mid_point[1], f"{edge_length:.1f}", 
                fontsize=10, ha='center', va='center',
                bbox=dict(boxstyle="round", facecolor="lightblue", alpha=0.7))
    
    # 标注顶点坐标
    for i, vertex in enumerate(rotated_vertices):
        ax3.plot(vertex[0], vertex[1], "ko", markersize=8)
        ax3.text(vertex[0] + 2, vertex[1] + 2, f"V{i + 1}\n({vertex[0]:.1f},{vertex[1]:.1f})", 
                fontsize=9, fontweight="bold")
    
    # 突出显示接触点
    ax3.plot(contact[0], contact[1], "ro", markersize=15, label=f"接触点: {contact_type}")
    
    ax3.set_xlim(-40, 40)
    ax3.set_ylim(-15, 15)
    ax3.legend()
    
    # 图4: 数据统计
    ax4.axis('off')
    ax4.set_title(f"89.5度状态数据统计", fontsize=14, fontweight="bold")
    
    # 创建数据表格
    data_text = f"""
测试角度: {angle}°

几何信息:
• 旋转中心: ({sim.rotation_center[0]:.2f}, {sim.rotation_center[1]:.2f})
• 几何中心: ({sim.geometric_center[0]:.2f}, {sim.geometric_center[1]:.2f})
• X方向偏移: {sim.rotation_center_x_offset:.2f} mm

接触点信息:
• 接触点类型: {contact_type}
• 接触点坐标: ({contact[0]:.2f}, {contact[1]:.2f})
• 过辊A动态切点: ({contact_A_dynamic[0]:.2f}, {contact_A_dynamic[1]:.2f})

长度分量:
• 过辊A到接触点切线: {tangent_A:.2f} mm
• 过辊A-B切线长度: {tangent_AB:.2f} mm  
• 过辊A圆弧长度: {arc_A:.2f} mm
• 过辊B圆弧长度: {arc_B:.2f} mm
• 总薄膜长度: {tangent_A + tangent_AB + arc_A + arc_B:.2f} mm

验证信息:
• 切线是否有效: {'是' if sim.is_valid_tangent_point(sim.A, contact, rotated_vertices) else '否'}
• 过辊切点X坐标: {contact_A_dynamic[0]:.2f} (应 > 0.5)
• 接触点选择原则: 最左侧点优先
"""
    
    ax4.text(0.05, 0.95, data_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    print(f"\n89.5度切线与菱形卷正状态图像已生成完成!")


if __name__ == "__main__":
    # 运行测试
    sim, contact, contact_type, tangent_length = test_89_5_degree_tangent()
    
    print(f"\n测试总结:")
    print(f"在89.5度角度下:")
    print(f"• 接触点类型: {contact_type}")
    print(f"• 切线长度: {tangent_length:.2f} mm")
    print(f"• 当前逻辑运行正常，成功生成了89.5度切线与菱形卷正状态的图像")
