#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菱形内切圆优化方案总结报告
展示详细的优化效果和几何计算结果
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, FancyBboxPatch
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


def create_optimization_summary():
    """创建优化方案总结图表"""
    
    # 原始菱形顶点
    original_vertices = np.array([
        [-30, 0],   # V1 (左锐角)
        [-20, -4],  # V2 (左下钝角)
        [20, -4],   # V3 (右下钝角)
        [30, 0],    # V4 (右锐角)
        [20, 4],    # V5 (右上钝角)
        [-20, 4],   # V6 (左上钝角)
    ])
    
    # 内切圆参数
    sharp_radius = 0.8
    blunt_radius = 12.0
    
    # 计算结果（从之前的运行结果）
    sharp_distance_diff = 1.354  # mm
    blunt_distance_diff = 0.221  # mm
    total_savings = 3.590  # mm
    
    # 内切圆圆心位置（从计算结果）
    circle_centers = [
        [-27.85, 0.00],    # V1 锐角圆心
        [-17.69, 8.00],    # V2 钝角圆心
        [17.69, 8.00],     # V3 钝角圆心
        [27.85, 0.00],     # V4 锐角圆心
        [17.69, -8.00],    # V5 钝角圆心
        [-17.69, -8.00],   # V6 钝角圆心
    ]
    
    # 创建图表
    fig = plt.figure(figsize=(20, 12))
    
    # 主要对比图
    ax1 = plt.subplot2grid((3, 4), (0, 0), colspan=2, rowspan=2)
    ax1.set_aspect('equal')
    ax1.grid(True, alpha=0.3)
    ax1.set_title('菱形内切圆优化方案对比', fontsize=16, fontweight='bold')
    
    # 绘制原始菱形
    original_poly = Polygon(original_vertices, fill=False, edgecolor='blue', 
                           linewidth=3, linestyle='--', alpha=0.7, label='原始菱形')
    ax1.add_patch(original_poly)
    
    # 绘制内切圆
    colors = ['red', 'green', 'green', 'red', 'green', 'green']
    radii = [sharp_radius, blunt_radius, blunt_radius, sharp_radius, blunt_radius, blunt_radius]
    
    for i, (center, color, radius) in enumerate(zip(circle_centers, colors, radii)):
        circle = Circle(center, radius, fill=True, facecolor=color, 
                       edgecolor=color, linewidth=2, alpha=0.4)
        ax1.add_patch(circle)
        
        # 标注圆心和半径
        ax1.plot(center[0], center[1], 'o', color=color, markersize=8)
        corner_type = "锐角" if i in [0, 3] else "钝角"
        ax1.text(center[0]+1, center[1]+1, f'{corner_type}\nr={radius}', 
                fontsize=10, color=color, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # 标注原始顶点
    for i, vertex in enumerate(original_vertices):
        ax1.plot(vertex[0], vertex[1], 'bo', markersize=10)
        ax1.text(vertex[0]+2, vertex[1]+2, f'V{i+1}', fontsize=12, fontweight='bold')
    
    # 添加图例
    sharp_patch = mpatches.Patch(color='red', alpha=0.4, label=f'锐角内切圆 (r={sharp_radius}mm)')
    blunt_patch = mpatches.Patch(color='green', alpha=0.4, label=f'钝角内切圆 (r={blunt_radius}mm)')
    original_patch = mpatches.Patch(color='blue', alpha=0.7, label='原始菱形轮廓')
    ax1.legend(handles=[sharp_patch, blunt_patch, original_patch], loc='upper right')
    
    ax1.set_xlim(-40, 40)
    ax1.set_ylim(-20, 20)
    
    # 距离差分析图
    ax2 = plt.subplot2grid((3, 4), (0, 2), colspan=2)
    vertices_labels = ['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)']
    distance_diffs = [sharp_distance_diff, blunt_distance_diff, blunt_distance_diff, 
                     sharp_distance_diff, blunt_distance_diff, blunt_distance_diff]
    colors_bar = ['red', 'green', 'green', 'red', 'green', 'green']
    
    bars = ax2.bar(vertices_labels, distance_diffs, color=colors_bar, alpha=0.7)
    ax2.set_title('各顶点距离差分析', fontsize=14, fontweight='bold')
    ax2.set_ylabel('距离差 (mm)')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, diff in zip(bars, distance_diffs):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{diff:.3f}mm', ha='center', va='bottom', fontweight='bold')
    
    # 优化效果统计
    ax3 = plt.subplot2grid((3, 4), (1, 2), colspan=2)
    categories = ['锐角部分', '钝角部分', '总计']
    savings = [2.708, 0.882, 3.590]  # mm
    colors_pie = ['red', 'green', 'orange']
    
    bars = ax3.bar(categories, savings, color=colors_pie, alpha=0.7)
    ax3.set_title('材料节省效果', fontsize=14, fontweight='bold')
    ax3.set_ylabel('节省量 (mm)')
    ax3.grid(True, alpha=0.3)
    
    for bar, saving in zip(bars, savings):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{saving:.3f}mm', ha='center', va='bottom', fontweight='bold')
    
    # 详细参数表
    ax4 = plt.subplot2grid((3, 4), (2, 0), colspan=4)
    ax4.axis('off')
    
    # 创建参数表格
    table_data = [
        ['参数项目', '锐角 (V1, V4)', '钝角 (V2, V3, V5, V6)', '说明'],
        ['内角度数', '43.6°', '158.2°', '原始菱形的内角'],
        ['内切圆半径', f'{sharp_radius} mm', f'{blunt_radius} mm', '设计参数'],
        ['原顶点到圆心距离', '2.15 mm', '12.22 mm', '几何计算结果'],
        ['距离差', f'{sharp_distance_diff:.3f} mm', f'{blunt_distance_diff:.3f} mm', '原顶点到内切圆的距离'],
        ['单个角节省', f'{sharp_distance_diff:.3f} mm', f'{blunt_distance_diff:.3f} mm', '每个角的材料节省'],
        ['总节省 (2个锐角)', f'{2*sharp_distance_diff:.3f} mm', '', '锐角部分总节省'],
        ['总节省 (4个钝角)', '', f'{4*blunt_distance_diff:.3f} mm', '钝角部分总节省'],
        ['整体优化效果', '', f'{total_savings:.3f} mm', '总材料节省量'],
    ]
    
    # 绘制表格
    table = ax4.table(cellText=table_data[1:], colLabels=table_data[0],
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data)):
        for j in range(len(table_data[0])):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            elif j == 1:  # 锐角列
                cell.set_facecolor('#ffebee')
            elif j == 2:  # 钝角列
                cell.set_facecolor('#e8f5e8')
            else:
                cell.set_facecolor('#f5f5f5')
    
    plt.suptitle('菱形凸轮内切圆优化方案 - 详细分析报告', fontsize=18, fontweight='bold', y=0.95)
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    
    return fig


def print_summary_report():
    """打印文字版总结报告"""
    print("\n" + "="*80)
    print("菱形凸轮内切圆优化方案 - 总结报告")
    print("="*80)
    
    print(f"\n📋 优化方案概述:")
    print(f"   • 将原始菱形的2个锐角用 r=0.8mm 的内切圆替代")
    print(f"   • 将原始菱形的4个钝角用 r=12.0mm 的内切圆替代")
    
    print(f"\n📐 几何分析结果:")
    print(f"   • 锐角 (V1, V4): 内角43.6°, 距离差1.354mm")
    print(f"   • 钝角 (V2, V3, V5, V6): 内角158.2°, 距离差0.221mm")
    
    print(f"\n💰 优化效果:")
    print(f"   • 锐角部分材料节省: 2.708mm")
    print(f"   • 钝角部分材料节省: 0.882mm")
    print(f"   • 总材料节省: 3.590mm")
    print(f"   • 平均每个角节省: 0.598mm")
    
    print(f"\n✅ 技术优势:")
    print(f"   • 消除尖角应力集中")
    print(f"   • 改善加工工艺性")
    print(f"   • 提高结构强度")
    print(f"   • 减少材料消耗")
    
    print(f"\n🔧 实施建议:")
    print(f"   • 锐角内切圆可采用小半径精密加工")
    print(f"   • 钝角内切圆可采用标准圆弧加工")
    print(f"   • 建议进行有限元分析验证应力分布")
    print(f"   • 可考虑进一步优化内切圆半径")
    
    print("="*80)


if __name__ == "__main__":
    # 生成可视化报告
    fig = create_optimization_summary()
    
    # 打印文字报告
    print_summary_report()
    
    # 显示图表
    plt.show()
    
    print(f"\n🎉 菱形内切圆优化方案分析完成！")
    print(f"📊 详细的可视化图表已生成，请查看图形窗口。")
