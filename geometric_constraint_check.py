#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查90度时的几何约束问题
验证薄膜路径是否会穿过菱形内部
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class GeometricConstraintChecker:
    def __init__(self):
        """初始化几何参数"""
        
        # 过辊参数
        self.A = np.array([0.5, 80.0])  # 过辊A位置
        self.roller_radius = 2.0  # 过辊半径
        
        # 原始菱形顶点
        self.original_vertices = np.array([
            [-30, 0],   # V1
            [-20, -4],  # V2
            [20, -4],   # V3
            [30, 0],    # V4
            [20, 4],    # V5
            [-20, 4],   # V6
        ])
        
        # 旋转中心
        self.geometric_center = np.mean(self.original_vertices, axis=0)  # (0, 0)
        rotation_center_x_offset = 2.0
        self.rotation_center = np.array([
            self.geometric_center[0] + rotation_center_x_offset,  # (2, 0)
            self.geometric_center[1],
        ])
        
        # 上方特殊点
        self.upper_point = np.array([
            self.geometric_center[0] + rotation_center_x_offset, 4  # (2, 4)
        ])
        
        print(f"过辊A位置: {self.A}")
        print(f"旋转中心: {self.rotation_center}")
        print(f"上方特殊点: {self.upper_point}")
    
    def rotate_point(self, point, angle_deg, center):
        """绕指定中心旋转点"""
        angle_rad = np.deg2rad(angle_deg)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        
        translated = point - center
        rotated = np.array([
            translated[0] * cos_a - translated[1] * sin_a,
            translated[0] * sin_a + translated[1] * cos_a
        ])
        return rotated + center
    
    def point_in_polygon(self, point, polygon_vertices):
        """检查点是否在多边形内部（射线法）"""
        x, y = point
        n = len(polygon_vertices)
        inside = False
        
        p1x, p1y = polygon_vertices[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_vertices[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def line_intersects_polygon(self, start_point, end_point, polygon_vertices):
        """检查线段是否与多边形相交（穿过内部）"""
        # 检查线段上的多个点是否在多边形内部
        num_samples = 100
        for i in range(1, num_samples):
            t = i / num_samples
            sample_point = start_point + t * (end_point - start_point)
            if self.point_in_polygon(sample_point, polygon_vertices):
                return True, sample_point
        return False, None
    
    def check_angle_constraint(self, angle_deg):
        """检查指定角度下的几何约束"""
        
        # 旋转顶点和上方点
        rotated_vertices = []
        for vertex in self.original_vertices:
            rotated_vertex = self.rotate_point(vertex, angle_deg, self.rotation_center)
            rotated_vertices.append(rotated_vertex)
        rotated_vertices = np.array(rotated_vertices)
        
        rotated_upper = self.rotate_point(self.upper_point, angle_deg, self.rotation_center)
        
        # 检查从过辊A到上方点的直线是否穿过菱形
        intersects, intersection_point = self.line_intersects_polygon(
            self.A, rotated_upper, rotated_vertices)
        
        # 找到最左侧的顶点
        leftmost_vertex_idx = np.argmin([v[0] for v in rotated_vertices])
        leftmost_vertex = rotated_vertices[leftmost_vertex_idx]
        
        # 检查从过辊A到最左侧顶点的直线是否穿过菱形
        intersects_leftmost, intersection_leftmost = self.line_intersects_polygon(
            self.A, leftmost_vertex, rotated_vertices)
        
        return {
            'angle': angle_deg,
            'rotated_vertices': rotated_vertices,
            'rotated_upper': rotated_upper,
            'leftmost_vertex': leftmost_vertex,
            'leftmost_vertex_idx': leftmost_vertex_idx,
            'upper_intersects': intersects,
            'upper_intersection': intersection_point,
            'leftmost_intersects': intersects_leftmost,
            'leftmost_intersection': intersection_leftmost
        }
    
    def visualize_constraint_check(self, angle_deg):
        """可视化几何约束检查"""
        
        result = self.check_angle_constraint(angle_deg)
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'角度 {angle_deg}° - 几何约束检查', fontsize=14, fontweight='bold')
        
        # 绘制过辊A
        roller_circle = Circle(self.A, self.roller_radius, 
                              fill=False, edgecolor='blue', linewidth=3)
        ax.add_patch(roller_circle)
        ax.plot(self.A[0], self.A[1], 'bo', markersize=10, label='过辊A')
        
        # 绘制菱形
        diamond_poly = Polygon(result['rotated_vertices'], fill=True, 
                              facecolor='lightgray', edgecolor='black', 
                              linewidth=2, alpha=0.5, label='菱形')
        ax.add_patch(diamond_poly)
        
        # 标注顶点
        vertex_labels = ['V1', 'V2', 'V3', 'V4', 'V5', 'V6']
        for i, (vertex, label) in enumerate(zip(result['rotated_vertices'], vertex_labels)):
            color = 'red' if i == result['leftmost_vertex_idx'] else 'gray'
            size = 10 if i == result['leftmost_vertex_idx'] else 6
            ax.plot(vertex[0], vertex[1], 'o', color=color, markersize=size)
            ax.text(vertex[0]+1, vertex[1]+1, label, fontsize=10, color=color, fontweight='bold')
        
        # 绘制上方特殊点
        ax.plot(result['rotated_upper'][0], result['rotated_upper'][1], 'co', 
               markersize=10, label='上方特殊点')
        ax.text(result['rotated_upper'][0]+1, result['rotated_upper'][1]+1, 
               '上方点', fontsize=10, color='cyan', fontweight='bold')
        
        # 绘制从过辊A到上方点的线
        line_color = 'red' if result['upper_intersects'] else 'green'
        line_style = '--' if result['upper_intersects'] else '-'
        ax.plot([self.A[0], result['rotated_upper'][0]], 
               [self.A[1], result['rotated_upper'][1]], 
               color=line_color, linewidth=3, linestyle=line_style,
               label=f'A→上方点 {"(穿过菱形)" if result["upper_intersects"] else "(不穿过)"}')
        
        # 绘制从过辊A到最左侧顶点的线
        leftmost_color = 'orange' if result['leftmost_intersects'] else 'purple'
        leftmost_style = '--' if result['leftmost_intersects'] else '-'
        ax.plot([self.A[0], result['leftmost_vertex'][0]], 
               [self.A[1], result['leftmost_vertex'][1]], 
               color=leftmost_color, linewidth=3, linestyle=leftmost_style,
               label=f'A→最左顶点 {"(穿过菱形)" if result["leftmost_intersects"] else "(不穿过)"}')
        
        # 标注交点
        if result['upper_intersects'] and result['upper_intersection'] is not None:
            ax.plot(result['upper_intersection'][0], result['upper_intersection'][1], 
                   'rx', markersize=12, markeredgewidth=3, label='上方点路径交点')
        
        if result['leftmost_intersects'] and result['leftmost_intersection'] is not None:
            ax.plot(result['leftmost_intersection'][0], result['leftmost_intersection'][1], 
                   'mx', markersize=12, markeredgewidth=3, label='最左顶点路径交点')
        
        # 设置坐标轴范围
        ax.set_xlim(-35, 35)
        ax.set_ylim(-25, 85)
        ax.legend()
        
        plt.tight_layout()
        plt.show()
        
        # 打印分析结果
        print(f"\n=== 角度 {angle_deg}° 几何约束分析 ===")
        print(f"上方特殊点位置: ({result['rotated_upper'][0]:.2f}, {result['rotated_upper'][1]:.2f})")
        print(f"最左侧顶点: {vertex_labels[result['leftmost_vertex_idx']]} ({result['leftmost_vertex'][0]:.2f}, {result['leftmost_vertex'][1]:.2f})")
        print(f"A→上方点路径穿过菱形: {'是' if result['upper_intersects'] else '否'}")
        print(f"A→最左顶点路径穿过菱形: {'是' if result['leftmost_intersects'] else '否'}")
        
        if result['upper_intersects']:
            print("⚠️  从过辊A到上方特殊点的直线穿过菱形内部，物理上不可行！")
        
        if not result['leftmost_intersects']:
            print("✅ 从过辊A到最左侧顶点的路径可行，应该选择此路径。")
        
        return result


def test_critical_angles():
    """测试关键角度的几何约束"""
    
    checker = GeometricConstraintChecker()
    
    # 测试关键角度
    critical_angles = [80, 85, 90, 95, 100]
    
    for angle in critical_angles:
        print(f"\n{'='*60}")
        print(f"检查角度: {angle}°")
        print(f"{'='*60}")
        checker.visualize_constraint_check(angle)


if __name__ == "__main__":
    print("几何约束检查 - 验证90度时的薄膜路径问题")
    test_critical_angles()
    print("\n检查完成！")
