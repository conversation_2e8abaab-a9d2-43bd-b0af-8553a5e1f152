#!/usr/bin/env python3
"""
测试80度到90度每0.5度的最左侧点切换情况
"""

import sys
import numpy as np
from main import HexagonFilmSimulation

def calculate_angle_from_contact_point(contact_point, roller_A, next_vertex):
    """
    以接触点为中心计算角度
    """
    # 从接触点指向过辊A的角度
    contact_to_roller = roller_A - contact_point
    tangent_angle = np.arctan2(contact_to_roller[1], contact_to_roller[0])
    tangent_angle_deg = np.rad2deg(tangent_angle)
    
    # 从接触点指向下一个顶点的角度
    contact_to_vertex = next_vertex - contact_point
    vertex_angle = np.arctan2(contact_to_vertex[1], contact_to_vertex[0])
    vertex_angle_deg = np.rad2deg(vertex_angle)
    
    # 计算角度差
    angle_diff = vertex_angle_deg - tangent_angle_deg
    
    # 处理角度跨越±180°边界的情况
    if angle_diff > 180:
        angle_diff -= 360
    elif angle_diff < -180:
        angle_diff += 360
    
    return tangent_angle_deg, vertex_angle_deg, angle_diff

def get_next_vertex_info(contact_type, rotated_vertices, rotated_upper):
    """
    根据接触点类型确定下一个顶点
    """
    vertex_sequence = [4, 3, 2, 1, 0, 5]  # V5->V4->V3->V2->V1->V6 的索引序列
    
    if "upper_point" in contact_type:
        next_vertex_idx = 4  # V5
        next_vertex = rotated_vertices[next_vertex_idx]
        next_vertex_name = "V5"
    else:
        # 如果是顶点，找到在序列中的下一个
        current_vertex_idx = None
        if "V5" in contact_type:
            current_vertex_idx = 4
        elif "V4" in contact_type:
            current_vertex_idx = 3
        elif "V3" in contact_type:
            current_vertex_idx = 2
        elif "V2" in contact_type:
            current_vertex_idx = 1
        elif "V1" in contact_type:
            current_vertex_idx = 0
        elif "V6" in contact_type:
            current_vertex_idx = 5

        if current_vertex_idx is not None:
            current_pos = vertex_sequence.index(current_vertex_idx)
            next_pos = (current_pos + 1) % len(vertex_sequence)
            next_vertex_idx = vertex_sequence[next_pos]
            next_vertex = rotated_vertices[next_vertex_idx]
            next_vertex_name = f"V{next_vertex_idx + 1}"
        else:
            # 如果无法确定，默认使用V5
            next_vertex_idx = 4
            next_vertex = rotated_vertices[next_vertex_idx]
            next_vertex_name = "V5"
    
    return next_vertex, next_vertex_name

def test_angle_range():
    """
    测试80度到90度每0.5度的情况
    """
    print("=== 80度到90度最左侧点切换检测 ===\n")
    
    # 初始化仿真
    sim = HexagonFilmSimulation()
    
    # 测试角度范围
    start_angle = 80.0
    end_angle = 90.0
    step = 0.5
    
    angles = np.arange(start_angle, end_angle + step, step)
    
    results = []
    
    print(f"{'角度':<6} {'接触点类型':<20} {'接触点坐标':<15} {'切线角度':<8} {'顶点角度':<8} {'角度差':<8} {'需要修正':<8}")
    print("-" * 85)
    
    for angle in angles:
        # 计算旋转参数
        theta_rad = np.deg2rad(angle)
        layer_num, accum_thickness = sim.calculate_layer_from_angle(angle)
        
        # 计算旋转后的顶点
        if accum_thickness > 0:
            current_vertices = sim.update_vertices_with_thickness(accum_thickness)
            rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
                theta_rad, current_vertices
            )
        else:
            rotated_vertices = sim.rotate_vertices_around_center(theta_rad)
        
        # 计算特殊点位的旋转位置
        rotated_upper, rotated_lower = sim.rotate_special_points_around_center(
            theta_rad, accum_thickness
        )
        
        # 使用原始逻辑找到最左侧点
        contact, contact_type = sim.find_leftmost_valid_contact_point(
            sim.A, rotated_vertices, rotated_upper, angle
        )
        
        # 获取下一个顶点信息
        next_vertex, next_vertex_name = get_next_vertex_info(
            contact_type, rotated_vertices, rotated_upper
        )
        
        # 计算角度
        tangent_angle_deg, vertex_angle_deg, angle_diff = calculate_angle_from_contact_point(
            contact, sim.A, next_vertex
        )
        
        # 判断是否需要修正
        needs_correction = 0 < angle_diff < 180
        
        # 格式化输出
        contact_coord = f"({contact[0]:.2f},{contact[1]:.2f})"
        correction_status = "是" if needs_correction else "否"
        
        print(f"{angle:<6.1f} {contact_type:<20} {contact_coord:<15} {tangent_angle_deg:<8.2f} {vertex_angle_deg:<8.2f} {angle_diff:<8.2f} {correction_status:<8}")
        
        # 保存结果
        results.append({
            'angle': angle,
            'contact_type': contact_type,
            'contact': contact,
            'tangent_angle': tangent_angle_deg,
            'vertex_angle': vertex_angle_deg,
            'angle_diff': angle_diff,
            'needs_correction': needs_correction,
            'next_vertex_name': next_vertex_name
        })
    
    # 分析切换点
    print("\n=== 切换点分析 ===")
    
    # 找到接触点类型的切换
    contact_type_changes = []
    for i in range(1, len(results)):
        if results[i]['contact_type'] != results[i-1]['contact_type']:
            contact_type_changes.append({
                'from_angle': results[i-1]['angle'],
                'to_angle': results[i]['angle'],
                'from_type': results[i-1]['contact_type'],
                'to_type': results[i]['contact_type']
            })
    
    if contact_type_changes:
        print("\n接触点类型切换:")
        for change in contact_type_changes:
            print(f"  {change['from_angle']}° -> {change['to_angle']}°: {change['from_type']} -> {change['to_type']}")
    else:
        print("\n在测试范围内没有发现接触点类型切换")
    
    # 找到需要修正的角度范围
    correction_ranges = []
    in_correction_range = False
    range_start = None
    
    for result in results:
        if result['needs_correction'] and not in_correction_range:
            # 开始需要修正的范围
            in_correction_range = True
            range_start = result['angle']
        elif not result['needs_correction'] and in_correction_range:
            # 结束需要修正的范围
            in_correction_range = False
            correction_ranges.append((range_start, results[results.index(result)-1]['angle']))
    
    # 如果最后还在修正范围内
    if in_correction_range:
        correction_ranges.append((range_start, results[-1]['angle']))
    
    print("\n需要修正的角度范围:")
    if correction_ranges:
        for start, end in correction_ranges:
            print(f"  {start}° - {end}°")
    else:
        print("  无需修正的角度")
    
    return results

if __name__ == "__main__":
    results = test_angle_range()
